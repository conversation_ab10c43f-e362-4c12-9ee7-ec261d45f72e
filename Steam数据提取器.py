import requests
import re
import json


def get_steam_app_info(app_id):
    """
    获取Steam应用信息
    
    Args:
        app_id (str): Steam应用ID
        
    Returns:
        str: API响应文本
    """
    url = f"https://steamui.com/api/get_appinfo.php?appid={app_id}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()  # 检查HTTP错误
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None


def extract_depot_manifest_info(text):
    """
    使用正则表达式提取depot和manifest信息
    
    Args:
        text (str): API响应文本
        
    Returns:
        list: 包含(depot_id, gid)元组的列表
    """
    # 正则表达式模式：匹配depot ID和对应的gid
    # 匹配模式：数字ID -> manifests -> public -> gid -> 数字值
    pattern = r'"(\d+)"\s*\{\s*(?:"dlcappid"\s*"\d+"\s*)?(?:"config"[^}]*\}\s*)?(?:"depotfromapp"[^}]*)?(?:"sharedinstall"[^}]*)?(?:"manifests"\s*\{[^}]*"public"\s*\{\s*"gid"\s*"(\d+)")'
    
    matches = re.findall(pattern, text, re.DOTALL)
    
    # 过滤掉非数字开头的depot（如"branches"等）
    filtered_matches = []
    for depot_id, gid in matches:
        # 确保depot_id是纯数字且长度合理（Steam depot ID通常是7-8位数字）
        if depot_id.isdigit() and len(depot_id) >= 6:
            filtered_matches.append((depot_id, gid))
    
    return filtered_matches


def main():
    """主函数"""
    app_id = "3489700"
    
    print(f"正在获取Steam应用 {app_id} 的信息...")
    
    # 获取API数据
    response_text = get_steam_app_info(app_id)
    
    if response_text is None:
        print("无法获取数据，程序退出")
        return
    
    print("API请求成功，正在提取数据...")
    
    # 提取depot和manifest信息
    depot_manifest_pairs = extract_depot_manifest_info(response_text)
    
    if depot_manifest_pairs:
        print("\n提取到的Depot和Manifest信息：")
        print("-" * 50)
        for depot_id, gid in depot_manifest_pairs:
            print(f"Depot ID: {depot_id}, Manifest GID: {gid}")
        
        print(f"\n总共找到 {len(depot_manifest_pairs)} 个depot-manifest对")
    else:
        print("未找到匹配的depot和manifest信息")
    
    # 可选：保存原始响应到文件以便调试
    with open("原始响应.txt", "w", encoding="utf-8") as f:
        f.write(response_text)
    print("\n原始API响应已保存到 '原始响应.txt'")


if __name__ == "__main__":
    main()
