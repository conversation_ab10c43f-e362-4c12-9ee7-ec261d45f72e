"appinfo"
{
	"appid"		"3489700"
	"common"
	{
		"name"		"Stellar Blade™"
		"type"		"Game"
		"name_localized"
		{
			"english"		"Stellar Blade™"
			"koreana"		"스텔라 블레이드™"
			"schinese"		"剑星"
			"tchinese"		"劍星"
		}
		"oslist"		"windows"
		"osarch"		"64"
		"osextended"		""
		"icon"		"57038a59c95ccee823169ffd580adef713543d46"
		"clienttga"		"9624f41b07ac97205bfc69aecb57f29a2bd0e2a4"
		"clienticon"		"154a70e60f5e3829922337e1c8a988be912c90f3"
		"releasestate"		"released"
		"languages"
		{
			"english"		"1"
			"german"		"1"
			"french"		"1"
			"italian"		"1"
			"koreana"		"1"
			"spanish"		"1"
			"schinese"		"1"
			"tchinese"		"1"
			"russian"		"1"
			"thai"		"1"
			"japanese"		"1"
			"portuguese"		"1"
			"polish"		"1"
			"danish"		"1"
			"dutch"		"1"
			"finnish"		"1"
			"norwegian"		"1"
			"swedish"		"1"
			"turkish"		"1"
			"arabic"		"1"
			"brazilian"		"1"
			"latam"		"1"
		}
		"content_descriptors"
		{
			"0"		"1"
			"1"		"2"
			"2"		"5"
		}
		"steam_deck_compatibility"
		{
			"category"		"3"
			"steamos_compatibility"		"2"
			"test_timestamp"		"1748649600"
			"tested_build_id"		"18621639"
			"tests"
			{
				"0"
				{
					"display"		"4"
					"token"		"#SteamDeckVerified_TestResult_DefaultControllerConfigFullyFunctional"
				}
				"1"
				{
					"display"		"4"
					"token"		"#SteamDeckVerified_TestResult_ControllerGlyphsMatchDeckDevice"
				}
				"2"
				{
					"display"		"4"
					"token"		"#SteamDeckVerified_TestResult_InterfaceTextIsLegible"
				}
				"3"
				{
					"display"		"4"
					"token"		"#SteamDeckVerified_TestResult_DefaultConfigurationIsPerformant"
				}
				"4"
				{
					"display"		"1"
					"token"		"#SteamDeckVerified_TestResult_FirstTimeSetupRequiresActiveInternetConnection"
				}
				"5"
				{
					"display"		"1"
					"token"		"#SteamDeckVerified_TestResult_ExternalControllersNotSupportedPrimaryPlayer"
				}
			}
			"steamos_tests"
			{
				"0"
				{
					"display"		"3"
					"token"		"#SteamOS_TestResult_GameStartupFunctional"
				}
				"1"
				{
					"display"		"1"
					"token"		"#SteamOS_TestResult_FirstTimeSetupRequiresActiveInternetConnection"
				}
				"2"
				{
					"display"		"1"
					"token"		"#SteamOS_TestResult_ExternalControllersNotSupportedPrimaryPlayer"
				}
			}
			"configuration"
			{
				"supported_input"		"gamepad"
				"requires_manual_keyboard_invoke"		"0"
				"requires_non_controller_launcher_nav"		"0"
				"primary_player_is_controller_slot_0"		"1"
				"non_deck_display_glyphs"		"0"
				"small_text"		"0"
				"requires_internet_for_setup"		"1"
				"requires_internet_for_singleplayer"		"0"
				"recommended_runtime"		"proton-9.0-4RC"
				"requires_h264"		"0"
				"requires_voice_files"		"0"
				"gamescope_frame_limiter_not_supported"		"0"
				"hdr_support"		"2"
			}
		}
		"controllertagwizard"		"1"
		"controller_support"		"full"
		"small_capsule"
		{
			"tchinese"		"acc581dd434cb9c0b602c35e04d4a86ec5e242f4/capsule_231x87_tchinese.jpg"
			"english"		"75914656cad450124a18fef914c5a5f4866ffb67/capsule_231x87.jpg"
			"koreana"		"d259131dd24a5aca2a771aeea10a4acc18a67255/capsule_231x87_koreana.jpg"
		}
		"header_image"
		{
			"english"		"header.jpg"
			"koreana"		"header_koreana.jpg"
			"tchinese"		"header_tchinese.jpg"
		}
		"library_assets"
		{
			"library_capsule"		"zh-tw,en,ko"
			"library_hero"		"en"
			"library_hero_blur"		"zh-tw,en,ko"
			"library_logo"		"zh-tw,ko,en"
			"library_header"		"zh-tw,en,ko"
			"logo_position"
			{
				"pinned_position"		"BottomLeft"
				"width_pct"		"38.24340977413988"
				"height_pct"		"103.81944444444446"
			}
		}
		"library_assets_full"
		{
			"library_capsule"
			{
				"image"
				{
					"tchinese"		"f33e11f3faa2d0e800102c8cc1046554d3edc5da/library_600x900_tchinese.jpg"
					"english"		"6f2bbe8f34fc283f42acbe4ab184e9a40f76ad51/library_600x900.jpg"
					"koreana"		"760a61821a3e307f3cb93a9c506f87feb130f303/library_600x900_koreana.jpg"
				}
				"image2x"
				{
					"tchinese"		"f33e11f3faa2d0e800102c8cc1046554d3edc5da/library_600x900_tchinese_2x.jpg"
					"english"		"6f2bbe8f34fc283f42acbe4ab184e9a40f76ad51/library_600x900_2x.jpg"
					"koreana"		"760a61821a3e307f3cb93a9c506f87feb130f303/library_600x900_koreana_2x.jpg"
				}
			}
			"library_hero"
			{
				"image2x"
				{
					"english"		"7f886b630308135ac990fefd005787729fec685f/library_hero_2x.jpg"
				}
				"image"
				{
					"english"		"7f886b630308135ac990fefd005787729fec685f/library_hero.jpg"
				}
			}
			"library_hero_blur"
			{
				"image"
				{
					"tchinese"		"89358f262c34f4b7acf2e3f678fe482dc93f48a8/library_hero_blur_tchinese.jpg"
					"english"		"7f886b630308135ac990fefd005787729fec685f/library_hero_blur.jpg"
					"koreana"		"c55343762f4440c8a2d752128bef36d2bf74cb51/library_hero_blur_koreana.jpg"
				}
			}
			"library_logo"
			{
				"image"
				{
					"tchinese"		"db7ef0cf453347e67233c85ebefc4346af7bef24/logo_tchinese.png"
					"koreana"		"71813a2dcf9167fc7614c8d5912ef3800127b7ae/logo_koreana.png"
					"english"		"291969d6747b765f62141bd170b42c7d9c4c238d/logo.png"
				}
				"image2x"
				{
					"tchinese"		"db7ef0cf453347e67233c85ebefc4346af7bef24/logo_tchinese_2x.png"
					"koreana"		"71813a2dcf9167fc7614c8d5912ef3800127b7ae/logo_koreana_2x.png"
					"english"		"291969d6747b765f62141bd170b42c7d9c4c238d/logo_2x.png"
				}
				"logo_position"
				{
					"pinned_position"		"BottomLeft"
					"width_pct"		"38.24340977413988"
					"height_pct"		"103.81944444444446"
				}
			}
			"library_header"
			{
				"image"
				{
					"tchinese"		"15aaa658b48937ef97da250bcee0781c57018040/library_header_tchinese.jpg"
					"english"		"3fef887cc0b7f8982253b25f42c6ebbba914bf94/library_header.jpg"
					"koreana"		"c83db44070c29991a02f278e95273c1feffd5538/library_header_koreana.jpg"
				}
				"image2x"
				{
					"tchinese"		"15aaa658b48937ef97da250bcee0781c57018040/library_header_tchinese_2x.jpg"
					"english"		"3fef887cc0b7f8982253b25f42c6ebbba914bf94/library_header_2x.jpg"
					"koreana"		"c83db44070c29991a02f278e95273c1feffd5538/library_header_koreana_2x.jpg"
				}
			}
		}
		"store_asset_mtime"		"1751863517"
		"associations"
		{
			"0"
			{
				"type"		"developer"
				"name"		"SHIFT UP Corporation"
			}
			"1"
			{
				"type"		"publisher"
				"name"		"PlayStation Publishing LLC (excluding China)"
			}
		}
		"primary_genre"		"1"
		"genres"
		{
			"0"		"1"
			"1"		"25"
			"2"		"3"
		}
		"category"
		{
			"category_2"		"1"
			"category_28"		"1"
			"category_55"		"1"
			"category_56"		"1"
			"category_57"		"1"
			"category_58"		"1"
			"category_59"		"1"
			"category_22"		"1"
			"category_23"		"1"
			"category_15"		"1"
			"category_61"		"1"
			"category_64"		"1"
			"category_65"		"1"
			"category_66"		"1"
			"category_67"		"1"
			"category_68"		"1"
			"category_69"		"1"
			"category_70"		"1"
			"category_74"		"1"
			"category_78"		"1"
			"category_29"		"1"
			"category_62"		"1"
		}
		"supported_languages"
		{
			"english"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"french"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"italian"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"german"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"spanish"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"arabic"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"dutch"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"swedish"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"latam"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"thai"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"danish"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"turkish"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"norwegian"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"finnish"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"brazilian"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"portuguese"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"russian"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"schinese"
			{
				"supported"		"true"
				"subtitles"		"true"
				"full_audio"		"true"
			}
			"tchinese"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
			"japanese"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"koreana"
			{
				"supported"		"true"
				"full_audio"		"true"
				"subtitles"		"true"
			}
			"polish"
			{
				"supported"		"true"
				"subtitles"		"true"
			}
		}
		"steam_release_date"		"1749678923"
		"community_visible_stats"		"1"
		"community_hub_visible"		"1"
		"gameid"		"3489700"
		"content_descriptors_including_dlc"
		{
			"0"		"1"
			"1"		"2"
			"2"		"5"
		}
		"store_tags"
		{
			"0"		"7208"
			"1"		"19"
			"2"		"1646"
			"3"		"29482"
			"4"		"4182"
			"5"		"1697"
			"6"		"4231"
			"7"		"21"
			"8"		"3835"
			"9"		"4106"
			"10"		"4295"
			"11"		"3993"
			"12"		"4191"
			"13"		"1742"
			"14"		"12095"
			"15"		"5030"
			"16"		"6971"
			"17"		"4559"
			"18"		"4252"
			"19"		"9994"
		}
		"review_score"		"8"
		"review_percentage"		"93"
	}
	"extended"
	{
		"developer"		"SHIFT UP Corporation"
		"publisher"		"PlayStation Publishing LLC (excluding China)"
		"homepage"		"https://www.playstation.com/games/stellar-blade/pc/"
		"listofdlc"		"3596170,3596180,3596190,3596200,3596210,3596220,3662250"
		"dlcavailableonstore"		"1"
	}
	"config"
	{
		"installdir"		"StellarBlade"
		"launch"
		{
			"0"
			{
				"executable"		"SB.exe"
				"arguments"		"-DistributionPlatform=Steam"
				"description_loc"
				{
					"english"		"Shipping Build"
				}
				"description"		"Shipping Build"
			}
		}
		"steamcontrollertemplateindex"		"1"
		"steamcontrollerconfigdetails"
		{
			"3451033965"
			{
				"controller_type"		"controller_xboxone"
				"enabled_branches"		"default"
				"use_action_block"		"false"
			}
			"3451034428"
			{
				"controller_type"		"controller_ps5"
				"enabled_branches"		"default"
				"use_action_block"		"false"
			}
			"3451035546"
			{
				"controller_type"		"controller_switch_pro"
				"enabled_branches"		"default"
				"use_action_block"		"false"
			}
			"3451040404"
			{
				"controller_type"		"controller_ps4"
				"enabled_branches"		"default"
				"use_action_block"		"false"
			}
			"3453465625"
			{
				"controller_type"		"controller_neptune"
				"enabled_branches"		"default"
				"use_action_block"		"false"
			}
		}
		"steamdecktouchscreen"		"1"
	}
	"depots"
	{
		"228989"
		{
			"config"
			{
				"oslist"		"windows"
			}
			"depotfromapp"		"228980"
			"sharedinstall"		"1"
		}
		"228990"
		{
			"config"
			{
				"oslist"		"windows"
			}
			"depotfromapp"		"228980"
			"sharedinstall"		"1"
		}
		"hasdepotsindlc"		"1"
		"3489701"
		{
			"manifests"
			{
				"public"
				{
					"gid"		"4947667343661089824"
					"size"		"62286173942"
					"download"		"58662567152"
				}
			}
		}
		"3596180"
		{
			"dlcappid"		"3596180"
			"manifests"
			{
				"public"
				{
					"gid"		"6319771942931301862"
					"size"		"267941934"
					"download"		"266481808"
				}
			}
		}
		"3596190"
		{
			"dlcappid"		"3596190"
			"manifests"
			{
				"public"
				{
					"gid"		"3119456928663392571"
					"size"		"25546741"
					"download"		"25543072"
				}
			}
		}
		"branches"
		{
			"public"
			{
				"buildid"		"19001071"
				"timeupdated"		"1751439588"
			}
		}
		"privatebranches"		"1"
	}
	"ufs"
	{
		"quota"		"600000000"
		"maxnumfiles"		"20"
		"savefiles"
		{
			"0"
			{
				"root"		"WindowsHome"
				"path"		"Documents/StellarBlade/{64BitSteamID}"
				"pattern"		"*.sav"
			}
			"1"
			{
				"root"		"WinAppDataLocal"
				"path"		"SB/Saved/SaveGames/{64BitSteamID}"
				"pattern"		"*.sav"
			}
			"2"
			{
				"root"		"WinAppDataLocal"
				"path"		"SB/Saved/SaveGames"
				"pattern"		"*.sav"
			}
		}
	}
}
